# terraform-users

<!-- BEGINNING OF PRE-COMMIT-TERRAFORM DOCS HOOK -->
## Scope


## Usage

### Clone project

```hcl
git clone  <repository_url>
cd <project_folder_name>/
## To do plan/apply your code create an PR on Azure DevOps. 
```  
### Installation of pre-commit for Ubuntu 20.04 and higher

```hcl
sudo apt update
sudo apt install -y unzip software-properties-common python3 python3-pip
python3 -m pip install --upgrade pip
pip/pip3 install -r requirements.txt
curl -L "$(curl -s https://api.github.com/repos/terraform-docs/terraform-docs/releases/latest | grep -o -E -m 1 "https://.+?-linux-amd64.tar.gz")" > terraform-docs.tgz && tar -xzf terraform-docs.tgz terraform-docs && rm terraform-docs.tgz && chmod +x terraform-docs && sudo mv terraform-docs /usr/bin/
cd <project_folder_name>/
pre-commit install
```

### Pipeline
From the pipeline you receive the variables:
- creator
- repository
- role_arn
A pipeline is included in this repository in the azure-pipelines.yml file.

Once an PR on main is approved, 3 stages are triggered:
#### terraform-link:
  - terraform_lint
  - terraform fmt -check
  - terraform validate
#### publish_tag:
  - create the tag using versioning configured in the package.json file


### Versioned

Once the project has been created and before working on a new improvement or fix, the following keys must be configured in the package.json file:
- name (project name)
- description (project description)
- type repository (git)
- repository url (repository url)
- version (place the version that will be used for the tag)

See [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0/)

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | 5.81.0 |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_efs_id"></a> [efs\_id](#output\_efs\_id) | n/a |
| <a name="output_role_arn_api"></a> [role\_arn\_api](#output\_role\_arn\_api) | n/a |
| <a name="output_role_arn_indexer"></a> [role\_arn\_indexer](#output\_role\_arn\_indexer) | n/a |
| <a name="output_sqs_queue_url"></a> [sqs\_queue\_url](#output\_sqs\_queue\_url) | n/a |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_cluster_name"></a> [cluster\_name](#input\_cluster\_name) | EKS cluster name where the service will be deployed | `string` | n/a | yes |
| <a name="input_efs_create"></a> [efs\_create](#input\_efs\_create) | n/a | `bool` | `false` | no |
| <a name="input_extra_tags"></a> [extra\_tags](#input\_extra\_tags) | extra tags to append to conventional ones | `map(string)` | `{}` | no |
| <a name="input_s3_bucket_create"></a> [s3\_bucket\_create](#input\_s3\_bucket\_create) | n/a | `bool` | `false` | no |
| <a name="input_s3_bucket_name"></a> [s3\_bucket\_name](#input\_s3\_bucket\_name) | n/a | `string` | `"ai-core-models"` | no |
| <a name="input_sa_name_api"></a> [sa\_name\_api](#input\_sa\_name\_api) | n/a | `string` | `"ai-core-api"` | no |
| <a name="input_sa_name_indexer"></a> [sa\_name\_indexer](#input\_sa\_name\_indexer) | n/a | `string` | `"ai-core-indexer"` | no |
| <a name="input_sqs_message_retention_seconds"></a> [sqs\_message\_retention\_seconds](#input\_sqs\_message\_retention\_seconds) | n/a | `number` | `604800` | no |
| <a name="input_sqs_name"></a> [sqs\_name](#input\_sqs\_name) | n/a | `string` | `""` | no |
| <a name="input_sqs_visibility_timeout_seconds"></a> [sqs\_visibility\_timeout\_seconds](#input\_sqs\_visibility\_timeout\_seconds) | n/a | `number` | `2400` | no |
| <a name="input_tenant_country"></a> [tenant\_country](#input\_tenant\_country) | ISO 3166 alpha-3 code of country | `string` | n/a | yes |
| <a name="input_tenant_name"></a> [tenant\_name](#input\_tenant\_name) | tenant name, which is client brand name with symbols stripped for compatibility | `string` | n/a | yes |  



## Resources


- resource.aws_efs_file_system.data (efs.tf#1)
- resource.aws_efs_mount_target.alpha (efs.tf#14)
- resource.aws_eks_pod_identity_association.api (iam-role-api.tf#33)
- resource.aws_eks_pod_identity_association.indexer (iam-role-daemon.tf#32)
- resource.aws_iam_role.api (iam-role-api.tf#2)
- resource.aws_iam_role.indexer (iam-role-daemon.tf#1)
- resource.aws_iam_role_policies_exclusive.api (iam-role-api.tf#28)
- resource.aws_iam_role_policies_exclusive.indexer (iam-role-daemon.tf#27)
- resource.aws_iam_role_policy.api (iam-role-api.tf#23)
- resource.aws_iam_role_policy.indexer (iam-role-daemon.tf#22)
- resource.aws_security_group.efs (sg.tf#1)
- data source.aws_eks_cluster.this (data.tf#1)
- data source.aws_iam_policy_document.api (iam-policy.tf#1)
- data source.aws_subnets.eks_vpc (data.tf#9)
- data source.aws_vpc.eks_vpc (data.tf#5)  


## Contributing

Pull requests are welcome. For major changes, please open an issue first to discuss what you would like to change.
Please make sure to update tests as appropriate.


## License
[MPL](https://choosealicense.com/licenses/mpl-2.0/)
<!-- END OF PRE-COMMIT-TERRAFORM DOCS HOOK -->
