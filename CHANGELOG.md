# Changelog

All notable changes to this project will be documented in this file.

## [0.1.0] - 2024-12-18

### 🚀 Features

- Add initial role and policy for api and indexer
- Add EFS and accompanying SecurityGroup for NFS access
- Add s3 bucket for models
- Add SQS queue for api<>indexer async work

### ⚙️ Miscellaneous Tasks

- Add providers, variables, output and local variables
- Add scaffolind files like pipeline, readme and pre-commit
- Fix pre-commit lints

<!-- generated by git-cliff -->
