module "sqs_dev_v2" {
  source                     = "terraform-aws-modules/sqs/aws"
  version                    = "4.2.1"
  name                       = length(var.sqs_dev_v2_name) > 0 ? var.sqs_dev_v2_name : "${local.tenant_fullname}-core-ai-v2"
  visibility_timeout_seconds = var.sqs_visibility_timeout_seconds
  message_retention_seconds  = var.sqs_message_retention_seconds

  tags = merge(var.extra_tags, local.default_tags)
}

module "sqs_usage" {
  source                     = "terraform-aws-modules/sqs/aws"
  version                    = "4.2.1"
  name                       = length(var.sqs_usage_name) > 0 ? var.sqs_usage_name : "${local.tenant_fullname}-core-ai-usage-v2"
  visibility_timeout_seconds = var.sqs_visibility_timeout_seconds
  message_retention_seconds  = var.sqs_message_retention_seconds

  tags = merge(var.extra_tags, local.default_tags)
}