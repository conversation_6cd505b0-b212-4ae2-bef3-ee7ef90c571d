module "sqs" {
  source                     = "terraform-aws-modules/sqs/aws"
  version                    = "4.2.1"
  name                       = length(var.sqs_name) > 0 ? var.sqs_name : "${local.tenant_fullname}-core-ai"
  visibility_timeout_seconds = var.sqs_visibility_timeout_seconds
  message_retention_seconds  = var.sqs_message_retention_seconds

  tags = merge(var.extra_tags, local.default_tags)
}