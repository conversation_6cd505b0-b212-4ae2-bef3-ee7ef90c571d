{"Role": {"Path": "/", "RoleName": "ai-core-api", "RoleId": "AROAZFBLU3QHVALF3HLZW", "Arn": "arn:aws:iam::************:role/ai-core-api", "CreateDate": "2025-05-26T02:42:39+00:00", "AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Principal": {"Service": "pods.eks.amazonaws.com"}, "Action": ["sts:TagSession", "sts:<PERSON><PERSON>Role"]}, {"Effect": "Allow", "Principal": {"Federated": "arn:aws:iam::************:oidc-provider/oidc.eks.us-east-1.amazonaws.com/id/5A1F241C25F12C74F2F7312C53044EBB"}, "Action": "sts:AssumeRoleWithWebIdentity", "Condition": {"StringEquals": {"oidc.eks.us-east-1.amazonaws.com/id/5A1F241C25F12C74F2F7312C53044EBB:aud": "sts.amazonaws.com", "oidc.eks.us-east-1.amazonaws.com/id/5A1F241C25F12C74F2F7312C53044EBB:sub": "system:serviceaccount:sudameris-py:ai-core-api"}}}]}, "MaxSessionDuration": 3600, "Tags": [{"Key": "Terraform", "Value": "true"}, {"Key": "Country", "Value": "Global"}, {"Key": "Service", "Value": "ai-core-be"}, {"Key": "Tenant", "Value": "sudam<PERSON><PERSON>"}], "RoleLastUsed": {"LastUsedDate": "2025-08-04T21:46:31+00:00", "Region": "us-east-1"}}}