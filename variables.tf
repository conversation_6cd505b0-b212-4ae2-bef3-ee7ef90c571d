variable "tenant_name" {
  type        = string
  description = "tenant name, which is client brand name with symbols stripped for compatibility"
}

variable "tenant_country" {
  type        = string
  description = "ISO 3166 alpha-3 code of country"
}

variable "cluster_name" {
  type        = string
  description = "EKS cluster name where the service will be deployed"
}

variable "sa_name_api" {
  type    = string
  default = "ai-core-api"
}

variable "sa_name_indexer" {
  type    = string
  default = "ai-core-indexer"
}

variable "extra_tags" {
  type        = map(string)
  description = "extra tags to append to conventional ones"
  default     = {}
}

variable "s3_bucket_name" {
  type    = string
  default = "ai-core-models"
}

variable "s3_bucket_create" {
  type    = bool
  default = false
}

variable "efs_create" {
  type    = bool
  default = false
}

variable "sqs_name" {
  type    = string
  default = ""
}

variable "sqs_visibility_timeout_seconds" {
  type    = number
  default = 2400
}

variable "sqs_message_retention_seconds" {
  type    = number
  default = 604800
}

variable "sqs_dev_v2_name" {
  type    = string
  default = ""
}

variable "sqs_usage_name" {
  type    = string
  default = ""
}
