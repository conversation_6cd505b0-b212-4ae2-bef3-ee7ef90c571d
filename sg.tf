resource "aws_security_group" "efs" {
  count       = var.efs_create ? 1 : 0
  name        = "${local.tenant_fullname}-ai-core-api-efs"
  description = "Security group for EFS mount targets"
  tags        = merge(var.extra_tags, local.default_tags)
  vpc_id      = data.aws_vpc.eks_vpc.id

  ingress {
    description = "Allow NFS traffic"
    from_port   = 2049
    to_port     = 2049
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    description = "Allow all outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}