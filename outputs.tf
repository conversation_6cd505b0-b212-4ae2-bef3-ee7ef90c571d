output "role_arn_indexer" {
  value = aws_iam_role.indexer.arn
}
output "role_arn_api" {
  value = aws_iam_role.api.arn
}

output "efs_id" {
  value = var.efs_create ? aws_efs_file_system.data[0].id : ""
}

output "sqs_queue_url" {
  value = module.sqs.queue_url
}

output "sqs_dev_v2_queue_url" {
  value = module.sqs_dev_v2.queue_url
}

output "sqs_usage_queue_url" {
  value = module.sqs_usage.queue_url
}
