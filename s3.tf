module "models" {
  count                                 = var.s3_bucket_create ? 1 : 0
  source                                = "terraform-aws-modules/s3-bucket/aws"
  version                               = "4.2.2"
  bucket                                = var.s3_bucket_name
  create_bucket                         = true
  force_destroy                         = false
  ignore_public_acls                    = true
  restrict_public_buckets               = true
  acl                                   = null
  attach_deny_insecure_transport_policy = true
  tags                                  = merge(var.extra_tags, local.default_tags)
}