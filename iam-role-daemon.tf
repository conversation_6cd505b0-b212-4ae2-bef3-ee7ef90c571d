resource "aws_iam_role" "indexer" {
  tags = merge(var.extra_tags, local.default_tags)
  name = "${local.tenant_fullname}-core-indexer"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "sts:TagSession",
          "sts:AssumeRole"
        ]
        Effect = "Allow"
        Principal = {
          Service = "pods.eks.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy" "indexer" {
  role   = aws_iam_role.indexer.name
  policy = data.aws_iam_policy_document.api.json
}

resource "aws_iam_role_policies_exclusive" "indexer" {
  role_name    = aws_iam_role.indexer.name
  policy_names = [aws_iam_role_policy.indexer.name]
}

resource "aws_eks_pod_identity_association" "indexer" {
  cluster_name    = var.cluster_name
  namespace       = local.tenant_fullname
  service_account = var.sa_name_indexer
  role_arn        = aws_iam_role.indexer.arn
  tags            = merge(var.extra_tags, local.default_tags)
}
