resource "aws_efs_file_system" "data" {
  count           = var.efs_create ? 1 : 0
  creation_token  = "quickCreated-eddb7fe7-7fe3-45c7-970f-d75fae3ef81b"
  throughput_mode = "bursting"

  lifecycle_policy {
    transition_to_ia = "AFTER_30_DAYS"
  }


  tags = merge(var.extra_tags, local.default_tags)
}

resource "aws_efs_mount_target" "alpha" {
  for_each        = var.efs_create ? toset(data.aws_subnets.eks_vpc.ids) : []
  file_system_id  = aws_efs_file_system.data[0].id
  security_groups = [aws_security_group.efs[0].id]
  subnet_id       = each.value
}
