# .pre-commit-config.yaml
default_stages: [commit]
repos:
    - repo: https://github.com/antonbabenko/pre-commit-terraform
      rev: v1.87.1
      hooks:
          - id: terraform_fmt
          - id: terraform_validate
          - id: terraform_docs
            args:
              - --hook-config=--path-to-file=README.md        # Valid UNIX path. I.e. ../TFDOC.md or docs/README.md etc.
              - --hook-config=--add-to-existing-file=true     # Boolean. true or false
              - --hook-config=--create-file-if-not-exist=true # Boolean. true or false
              - --args=--config=.terraform-docs.yml
    



