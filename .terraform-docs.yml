formatter: "markdown table" # this is required

version: ""

header-from: main.tf
footer-from: ""

recursive:
  enabled: false
  path: modules

sections:
  show:
    - all

content: |-
  ## Scope


  ## Usage

  ### Clone project

  ```hcl
  git clone  <repository_url>
  cd <project_folder_name>/
  ## To do plan/apply your code create an PR on Azure DevOps. 
  ```  
  ### Installation of pre-commit for Ubuntu 20.04 and higher

  ```hcl
  sudo apt update
  sudo apt install -y unzip software-properties-common python3 python3-pip
  python3 -m pip install --upgrade pip
  pip/pip3 install -r requirements.txt
  curl -L "$(curl -s https://api.github.com/repos/terraform-docs/terraform-docs/releases/latest | grep -o -E -m 1 "https://.+?-linux-amd64.tar.gz")" > terraform-docs.tgz && tar -xzf terraform-docs.tgz terraform-docs && rm terraform-docs.tgz && chmod +x terraform-docs && sudo mv terraform-docs /usr/bin/
  cd <project_folder_name>/
  pre-commit install
  ```

  ### Pipeline
  From the pipeline you receive the variables:
  - creator
  - repository
  - role_arn
  A pipeline is included in this repository in the azure-pipelines.yml file.
  
  Once an PR on main is approved, 3 stages are triggered:
  #### terraform-link:
    - terraform_lint
    - terraform fmt -check
    - terraform validate
  #### publish_tag:
    - create the tag using versioning configured in the package.json file

 
  ### Versioned
  
  Once the project has been created and before working on a new improvement or fix, the following keys must be configured in the package.json file:
  - name (project name)
  - description (project description)
  - type repository (git)
  - repository url (repository url)
  - version (place the version that will be used for the tag)

  See [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0/)

  {{ .Providers }}

  {{ .Outputs }}

  {{ .Inputs }}  
  

  
  ## Resources

  {{ range .Module.Resources }}
  - {{ .GetMode }}.{{ .Spec }} ({{ .Position.Filename }}#{{ .Position.Line }})
  {{- end }}  

  
  ## Contributing

  Pull requests are welcome. For major changes, please open an issue first to discuss what you would like to change.
  Please make sure to update tests as appropriate.
  

  ## License
  [MPL](https://choosealicense.com/licenses/mpl-2.0/)
  
output:
  file: ""
  mode: inject
  template: |-
    <!-- BEGIN_TF_DOCS -->
    {{ .Content }}
    <!-- END_TF_DOCS -->    

output-values:
  enabled: false
  from: ""

sort:
  enabled: true
  by: name

settings:
  anchor: true
  color: true
  default: true
  description: false
  escape: true
  hide-empty: false
  html: true
  indent: 2
  lockfile: true
  read-comments: true
  required: true
  sensitive: true
  type: true