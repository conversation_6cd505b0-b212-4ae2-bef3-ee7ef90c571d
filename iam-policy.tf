data "aws_iam_policy_document" "api" {
  statement {
    sid     = "AllowS3Models"
    effect  = "Allow"
    actions = ["s3:GetObject", "s3:List*"]
    resources = [
      "arn:aws:s3:::${var.s3_bucket_name}",
      "arn:aws:s3:::${var.s3_bucket_name}/*",
    ]
  }

  statement {
    sid    = "SQS"
    effect = "Allow"
    actions = [
      "sqs:DeleteMessage",
      "sqs:GetQueueAttributes",
      "sqs:GetQueueUrl",
      "sqs:ReceiveMessage",
      "sqs:SendMessage",
    ]
    resources = [
      module.sqs.queue_arn,
      module.sqs_dev_v2.queue_arn,
      module.sqs_usage.queue_arn
    ]
  }
}
